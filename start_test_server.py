#!/usr/bin/env python3
"""
测试服务器启动脚本
用于测试 TTS API 服务器的认证功能
"""

import os
import sys
import time
import subprocess
import threading
import requests
from pathlib import Path

def create_mock_tts():
    """创建一个模拟的 TTS 类用于测试"""
    class MockIndexTTS:
        def __init__(self, *args, **kwargs):
            print("MockIndexTTS 初始化")
            self.speaker_dict = {}
        
        def registry_speaker(self, name, paths):
            print(f"注册 speaker: {name}, paths: {paths}")
            self.speaker_dict[name] = paths
        
        async def infer_with_ref_audio_embed(self, character, text):
            print(f"模拟 TTS 生成: character={character}, text={text}")
            # 返回模拟的音频数据
            import numpy as np
            sr = 24000
            duration = 2.0  # 2秒
            samples = int(sr * duration)
            # 生成简单的正弦波
            t = np.linspace(0, duration, samples)
            wav = np.sin(2 * np.pi * 440 * t) * 0.5  # 440Hz 正弦波
            return sr, wav.astype(np.float32)
        
        async def infer(self, audio_paths, text):
            print(f"模拟 TTS 生成: audio_paths={audio_paths}, text={text}")
            import numpy as np
            sr = 24000
            duration = 2.0
            samples = int(sr * duration)
            t = np.linspace(0, duration, samples)
            wav = np.sin(2 * np.pi * 440 * t) * 0.5
            return sr, wav.astype(np.float32)
    
    return MockIndexTTS

def patch_imports():
    """修补导入，使用模拟的 TTS 类"""
    import sys
    from unittest.mock import MagicMock
    
    # 创建模拟模块
    mock_indextts = MagicMock()
    mock_indextts.infer_vllm.IndexTTS = create_mock_tts()
    
    # 添加到 sys.modules
    sys.modules['indextts'] = mock_indextts
    sys.modules['indextts.infer_vllm'] = mock_indextts.infer_vllm

def start_test_server():
    """启动测试服务器"""
    print("正在启动测试服务器...")
    
    # 修补导入
    patch_imports()
    
    # 导入并启动服务器
    import uvicorn
    from api_server import app
    
    # 启动服务器
    uvicorn.run(
        app=app,
        host="127.0.0.1",
        port=11996,
        log_level="info"
    )

def test_server():
    """测试服务器功能"""
    base_url = "http://127.0.0.1:11996"
    
    # 等待服务器启动
    print("等待服务器启动...")
    for i in range(30):
        try:
            response = requests.get(f"{base_url}/")
            if response.status_code == 200:
                print("✓ 服务器已启动")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("✗ 服务器启动超时")
        return
    
    print("\n=== 开始测试 ===")
    
    # 测试登录
    print("\n1. 测试登录...")
    try:
        response = requests.post(f"{base_url}/api/login", 
                               json={"password": "tts123456"})
        if response.status_code == 200:
            data = response.json()
            token = data.get("token")
            print(f"✓ 登录成功，token: {token[:20] if token else 'None'}...")
            
            # 测试获取 speakers
            print("\n2. 测试获取 speakers...")
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{base_url}/api/speakers", headers=headers)
            if response.status_code == 200:
                print("✓ 获取 speakers 成功")
                print(f"数据: {response.json()}")
            else:
                print(f"✗ 获取 speakers 失败: {response.status_code}")
                print(f"响应: {response.text}")
        else:
            print(f"✗ 登录失败: {response.status_code}")
            print(f"响应: {response.text}")
    except Exception as e:
        print(f"✗ 测试异常: {e}")
    
    print(f"\n访问调试页面: {base_url}/debug")
    print(f"访问登录页面: {base_url}/")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 运行测试
        test_server()
    else:
        # 启动服务器
        start_test_server()
