import os
# os.environ["CUDA_VISIBLE_DEVICES"] = "7"

import asyncio
import io
import traceback
from fastapi import FastAPI, Request, Response, File, UploadFile, Form, HTTPException, Depends
from fastapi.responses import JSONResponse, StreamingResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from contextlib import asynccontextmanager
import uvicorn
import argparse
import json
import asyncio
import time
import numpy as np
import soundfile as sf
import shutil
import uuid
import hashlib
import secrets
from typing import List, Dict, Any, Optional

from indextts.infer_vllm import IndexTTS

tts = None
SPEAKER_PATH = ""
ASSETS_DIR = ""

# 密码验证相关
PASSWORD = "tts123456"  # 默认密码，可以通过环境变量或命令行参数修改
active_sessions = set()  # 存储活跃的会话token
security = HTTPBearer(auto_error=False)

@asynccontextmanager
async def lifespan(app: FastAPI):
    global tts, SPEAKER_PATH, ASSETS_DIR
    cfg_path = os.path.join(args.model_dir, "config.yaml")
    tts = IndexTTS(model_dir=args.model_dir, cfg_path=cfg_path, gpu_memory_utilization=args.gpu_memory_utilization)

    current_file_path = os.path.abspath(__file__)
    cur_dir = os.path.dirname(current_file_path)
    ASSETS_DIR = os.path.join(cur_dir, "assets")
    
    # 确保assets目录存在
    if not os.path.exists(ASSETS_DIR):
        os.makedirs(ASSETS_DIR)
    
    SPEAKER_PATH = os.path.join(ASSETS_DIR, "speaker.json")
    if os.path.exists(SPEAKER_PATH):
        speaker_dict = json.load(open(SPEAKER_PATH, 'r'))

        for speaker, audio_paths in speaker_dict.items():
            tts.registry_speaker(speaker, audio_paths)
    else:
        # 如果speaker.json不存在，创建一个空的
        with open(SPEAKER_PATH, 'w') as f:
            json.dump({}, f)
    yield
    # Clean up the ML models and release the resources
    # ml_models.clear()

app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 挂载静态文件目录
app.mount("/assets", StaticFiles(directory="assets"), name="assets")

# 认证相关函数
def generate_session_token():
    """生成会话token"""
    return secrets.token_urlsafe(32)

def verify_password(password: str) -> bool:
    """验证密码"""
    return password == PASSWORD

async def get_current_session(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """验证当前会话"""
    print(f"[DEBUG] 认证检查 - credentials: {credentials}")
    if not credentials:
        print("[DEBUG] 未提供认证信息")
        raise HTTPException(status_code=401, detail="未提供认证信息")

    token = credentials.credentials
    print(f"[DEBUG] 提取的token: {token[:20] if token else 'None'}...")
    print(f"[DEBUG] 当前活跃会话数量: {len(active_sessions)}")
    print(f"[DEBUG] token是否在活跃会话中: {token in active_sessions}")

    if token not in active_sessions:
        print(f"[DEBUG] 会话已过期或无效")
        raise HTTPException(status_code=401, detail="会话已过期或无效")

    print(f"[DEBUG] 认证成功")
    return token

# 登录页面
@app.get("/")
async def get_login():
    return FileResponse("assets/login.html")

# 主页面（需要认证）
@app.get("/main")
async def get_main(session: str = Depends(get_current_session)):
    return FileResponse("assets/index.html")

# 调试页面（无需认证）
@app.get("/debug")
async def get_debug():
    return FileResponse("assets/debug.html")

# 登录接口
@app.post("/api/login")
async def login(request: Request):
    try:
        data = await request.json()
        password = data.get("password", "")
        print(f"[DEBUG] 登录尝试，密码长度: {len(password)}")

        if verify_password(password):
            token = generate_session_token()
            active_sessions.add(token)
            print(f"[DEBUG] 登录成功，生成token: {token[:20]}...")
            print(f"[DEBUG] 当前活跃会话数量: {len(active_sessions)}")
            return {"status": "success", "token": token}
        else:
            print(f"[DEBUG] 密码验证失败")
            return JSONResponse(
                status_code=401,
                content={"status": "error", "message": "密码错误"}
            )
    except Exception as ex:
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": "登录失败"}
        )

# 登出接口
@app.post("/api/logout")
async def logout(session: str = Depends(get_current_session)):
    active_sessions.discard(session)
    return {"status": "success", "message": "已登出"}

# 获取所有speaker
@app.get("/api/speakers")
async def get_speakers(session: str = Depends(get_current_session)):
    global SPEAKER_PATH
    try:
        with open(SPEAKER_PATH, 'r') as f:
            speaker_dict = json.load(f)
        return speaker_dict
    except Exception as ex:
        tb_str = ''.join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(tb_str)
            }
        )

# 添加新的speaker
@app.post("/api/speakers")
async def add_speaker(speaker_name: str = Form(...), audio_file: UploadFile = File(...), session: str = Depends(get_current_session)):
    global SPEAKER_PATH, ASSETS_DIR, tts
    try:
        # 读取现有的speaker.json
        with open(SPEAKER_PATH, 'r') as f:
            speaker_dict = json.load(f)
        
        # 创建speaker目录
        speaker_dir = os.path.join(ASSETS_DIR, speaker_name)
        if not os.path.exists(speaker_dir):
            os.makedirs(speaker_dir)
        
        # 生成唯一文件名
        filename = f"{uuid.uuid4()}.wav"
        file_path = os.path.join(speaker_dir, filename)
        relative_path = f"assets/{speaker_name}/{filename}"
        
        # 保存上传的文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(audio_file.file, buffer)
        
        # 更新speaker.json
        if speaker_name not in speaker_dict:
            speaker_dict[speaker_name] = [relative_path]
        else:
            speaker_dict[speaker_name].append(relative_path)
        
        # 保存更新后的speaker.json
        with open(SPEAKER_PATH, 'w') as f:
            json.dump(speaker_dict, f, indent=2)
        
        # 注册speaker到tts
        tts.registry_speaker(speaker_name, [relative_path])
        
        return {"status": "success", "message": f"Speaker {speaker_name} added successfully"}
    
    except Exception as ex:
        tb_str = ''.join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(tb_str)
            }
        )

# 删除speaker
@app.delete("/api/speakers/{speaker_name}")
async def delete_speaker(speaker_name: str, session: str = Depends(get_current_session)):
    global SPEAKER_PATH, ASSETS_DIR
    try:
        # 读取现有的speaker.json
        with open(SPEAKER_PATH, 'r') as f:
            speaker_dict = json.load(f)
        
        if speaker_name not in speaker_dict:
            return JSONResponse(
                status_code=404,
                content={
                    "status": "error",
                    "error": f"Speaker {speaker_name} not found"
                }
            )
        
        # 删除speaker对应的目录
        speaker_dir = os.path.join(ASSETS_DIR, speaker_name)
        if os.path.exists(speaker_dir):
            shutil.rmtree(speaker_dir)
        
        # 从speaker.json中删除
        del speaker_dict[speaker_name]
        
        # 保存更新后的speaker.json
        with open(SPEAKER_PATH, 'w') as f:
            json.dump(speaker_dict, f, indent=2)
        
        return {"status": "success", "message": f"Speaker {speaker_name} deleted successfully"}
    
    except Exception as ex:
        tb_str = ''.join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(tb_str)
            }
        )

# 删除音频文件
@app.delete("/api/speakers/{speaker_name}/files")
async def delete_audio_file(speaker_name: str, request: Request, session: str = Depends(get_current_session)):
    global SPEAKER_PATH
    try:
        data = await request.json()
        file_path = data.get("file_path")
        
        if not file_path:
            return JSONResponse(
                status_code=400,
                content={
                    "status": "error",
                    "error": "File path not provided"
                }
            )
        
        # 读取现有的speaker.json
        with open(SPEAKER_PATH, 'r') as f:
            speaker_dict = json.load(f)
        
        if speaker_name not in speaker_dict:
            return JSONResponse(
                status_code=404,
                content={
                    "status": "error",
                    "error": f"Speaker {speaker_name} not found"
                }
            )
        
        # 检查文件是否存在于speaker的列表中
        if file_path not in speaker_dict[speaker_name]:
            return JSONResponse(
                status_code=404,
                content={
                    "status": "error",
                    "error": f"File {file_path} not found for speaker {speaker_name}"
                }
            )
        
        # 删除实际文件
        actual_path = os.path.join(os.path.dirname(ASSETS_DIR), file_path)
        if os.path.exists(actual_path):
            os.remove(actual_path)
        
        # 从speaker.json中删除文件路径
        speaker_dict[speaker_name].remove(file_path)
        
        # 如果speaker没有音频文件了，删除整个speaker
        if len(speaker_dict[speaker_name]) == 0:
            del speaker_dict[speaker_name]
            speaker_dir = os.path.join(ASSETS_DIR, speaker_name)
            if os.path.exists(speaker_dir):
                shutil.rmtree(speaker_dir)
        
        # 保存更新后的speaker.json
        with open(SPEAKER_PATH, 'w') as f:
            json.dump(speaker_dict, f, indent=2)
        
        return {"status": "success", "message": f"File {file_path} deleted successfully"}
    
    except Exception as ex:
        tb_str = ''.join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(tb_str)
            }
        )

@app.post("/tts_url", responses={
    200: {"content": {"application/octet-stream": {}}},
    500: {"content": {"application/json": {}}}
})
async def tts_api_url(request: Request, session: str = Depends(get_current_session)):
    try:
        data = await request.json()
        text = data["text"]
        audio_paths = data["audio_paths"]

        global tts
        sr, wav = await tts.infer(audio_paths, text)
        
        with io.BytesIO() as wav_buffer:
            sf.write(wav_buffer, wav, sr, format='WAV')
            wav_bytes = wav_buffer.getvalue()

        return Response(content=wav_bytes, media_type="audio/wav")
    
    except Exception as ex:
        tb_str = ''.join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(tb_str)
            }
        )


@app.post("/tts", responses={
    200: {"content": {"application/octet-stream": {}}},
    500: {"content": {"application/json": {}}}
})
async def tts_api(request: Request, session: str = Depends(get_current_session)):
    try:
        data = await request.json()
        text = data["text"]
        character = data["character"]

        global tts
        sr, wav = await tts.infer_with_ref_audio_embed(character, text)
        
        with io.BytesIO() as wav_buffer:
            sf.write(wav_buffer, wav, sr, format='WAV')
            wav_bytes = wav_buffer.getvalue()

        return Response(content=wav_bytes, media_type="audio/wav")
    
    except Exception as ex:
        tb_str = ''.join(traceback.format_exception(type(ex), ex, ex.__traceback__))
        print(tb_str)
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "error": str(tb_str)
            }
        )

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--host", type=str, default="0.0.0.0")
    parser.add_argument("--port", type=int, default=11996)
    parser.add_argument("--model_dir", type=str, default="/path/to/IndexTeam/Index-TTS")
    parser.add_argument("--gpu_memory_utilization", type=float, default=0.65)
    parser.add_argument("--password", type=str, default="tts123456", help="访问密码")
    args = parser.parse_args()

    # 设置全局密码
    PASSWORD = args.password

    uvicorn.run(app=app, host=args.host, port=args.port)
