# TTS 系统密码验证功能

## 概述

已为 TTS 系统添加了完整的密码验证功能，包括：

1. **登录页面**：美观的密码输入界面
2. **认证系统**：基于 Bearer Token 的会话管理
3. **主页面保护**：所有 API 端点都需要认证
4. **标签页界面**：TTS 生成 + Speaker 管理

## 功能特性

### 🔐 安全特性
- 密码保护访问
- 安全的会话 token 管理
- 自动登出和重定向
- 防止未授权访问

### 🎵 TTS 功能
- 文本转语音生成
- Speaker 选择
- 实时进度显示
- 音频播放和下载

### 👥 Speaker 管理
- 添加/删除 Speaker
- 音频文件管理
- 音频预览功能

## 使用方法

### 1. 启动服务器

```bash
# 使用默认密码 (tts123456)
python api_server.py --model_dir /path/to/model --port 11996

# 使用自定义密码
python api_server.py --model_dir /path/to/model --port 11996 --password your_password
```

### 2. 访问系统

1. 打开浏览器访问 `http://localhost:11996`
2. 输入密码（默认：`tts123456`）
3. 登录后即可使用所有功能

### 3. 调试页面

访问 `http://localhost:11996/debug` 可以进入调试页面，用于测试认证功能。

## 测试方法

### 方法1：使用调试页面
1. 访问 `http://localhost:11996/debug`
2. 点击各个测试按钮
3. 查看控制台日志

### 方法2：使用测试脚本
```bash
# 启动测试服务器（使用模拟 TTS）
python start_test_server.py

# 在另一个终端运行测试
python start_test_server.py test

# 或者使用认证测试脚本
python test_auth.py
```

## 故障排除

### 问题：登录后显示"未提供认证信息"

**可能原因：**
1. Token 没有正确保存到 localStorage
2. 请求头格式不正确
3. 服务器端会话管理问题

**解决步骤：**

1. **检查浏览器控制台**
   - 打开开发者工具 (F12)
   - 查看 Console 标签页的错误信息
   - 查看 Network 标签页的请求详情

2. **使用调试页面**
   - 访问 `http://localhost:11996/debug`
   - 测试登录流程
   - 查看详细的调试信息

3. **检查服务器日志**
   - 查看服务器控制台输出
   - 寻找 `[DEBUG]` 开头的认证日志

4. **清除浏览器缓存**
   ```javascript
   // 在浏览器控制台执行
   localStorage.clear();
   location.reload();
   ```

### 问题：无法访问主页面

**检查项目：**
1. 确认密码正确
2. 检查网络连接
3. 确认服务器正常运行
4. 查看浏览器控制台错误

### 问题：TTS 生成失败

**可能原因：**
1. 没有可用的 Speaker
2. 模型文件缺失
3. 认证 token 过期

**解决方法：**
1. 先添加 Speaker
2. 检查模型路径
3. 重新登录获取新 token

## API 端点

### 认证相关
- `POST /api/login` - 用户登录
- `POST /api/logout` - 用户登出

### Speaker 管理
- `GET /api/speakers` - 获取 Speaker 列表
- `POST /api/speakers` - 添加新 Speaker
- `DELETE /api/speakers/{name}` - 删除 Speaker
- `DELETE /api/speakers/{name}/files` - 删除音频文件

### TTS 生成
- `POST /tts` - 使用 Speaker 生成语音
- `POST /tts_url` - 使用音频路径生成语音

### 页面路由
- `GET /` - 登录页面
- `GET /main` - 主页面（需要认证）
- `GET /debug` - 调试页面

## 配置选项

### 命令行参数
- `--host` - 服务器地址（默认：0.0.0.0）
- `--port` - 服务器端口（默认：11996）
- `--model_dir` - 模型目录路径
- `--gpu_memory_utilization` - GPU 内存使用率
- `--password` - 访问密码（默认：tts123456）

### 环境变量
可以通过环境变量设置密码：
```bash
export TTS_PASSWORD=your_password
python api_server.py
```

## 安全注意事项

1. **修改默认密码**：生产环境中务必修改默认密码
2. **HTTPS 部署**：生产环境建议使用 HTTPS
3. **定期更新密码**：建议定期更换访问密码
4. **会话管理**：Token 在服务器重启后会失效

## 开发说明

### 文件结构
```
assets/
├── index.html      # 主页面（标签页界面）
├── login.html      # 登录页面
└── debug.html      # 调试页面

api_server.py       # API 服务器（包含认证逻辑）
test_auth.py        # 认证测试脚本
start_test_server.py # 测试服务器启动脚本
```

### 认证流程
1. 用户输入密码 → 服务器验证
2. 验证成功 → 生成 session token
3. Token 保存到 localStorage
4. 后续请求携带 Bearer token
5. 服务器验证 token 有效性

### 扩展功能
- 可以添加用户管理
- 可以实现角色权限控制
- 可以添加 API 访问限制
- 可以集成第三方认证系统
