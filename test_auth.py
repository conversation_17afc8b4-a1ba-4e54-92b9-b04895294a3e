#!/usr/bin/env python3
"""
简单的认证测试脚本
用于测试 TTS API 服务器的密码验证功能
"""

import requests
import json

def test_auth():
    base_url = "http://localhost:11996"
    
    print("=== TTS API 认证测试 ===")
    
    # 1. 测试未认证访问
    print("\n1. 测试未认证访问...")
    try:
        response = requests.get(f"{base_url}/api/speakers")
        print(f"状态码: {response.status_code}")
        if response.status_code == 401:
            print("✓ 未认证访问被正确拒绝")
        else:
            print("✗ 未认证访问应该被拒绝")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 2. 测试错误密码登录
    print("\n2. 测试错误密码登录...")
    try:
        response = requests.post(f"{base_url}/api/login", 
                               json={"password": "wrongpassword"})
        print(f"状态码: {response.status_code}")
        if response.status_code == 401:
            print("✓ 错误密码被正确拒绝")
        else:
            print("✗ 错误密码应该被拒绝")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 3. 测试正确密码登录
    print("\n3. 测试正确密码登录...")
    token = None
    try:
        response = requests.post(f"{base_url}/api/login", 
                               json={"password": "tts123456"})
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success" and "token" in data:
                token = data["token"]
                print("✓ 正确密码登录成功")
                print(f"获得token: {token[:20]}...")
            else:
                print("✗ 登录响应格式错误")
        else:
            print("✗ 正确密码登录失败")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 4. 测试使用token访问API
    if token:
        print("\n4. 测试使用token访问API...")
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{base_url}/api/speakers", headers=headers)
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                print("✓ 使用token访问API成功")
                data = response.json()
                print(f"获得speakers数据: {len(data)} 个speaker")
            else:
                print("✗ 使用token访问API失败")
        except Exception as e:
            print(f"请求失败: {e}")
        
        # 5. 测试登出
        print("\n5. 测试登出...")
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.post(f"{base_url}/api/logout", headers=headers)
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                print("✓ 登出成功")
            else:
                print("✗ 登出失败")
        except Exception as e:
            print(f"请求失败: {e}")
        
        # 6. 测试登出后token是否失效
        print("\n6. 测试登出后token是否失效...")
        try:
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get(f"{base_url}/api/speakers", headers=headers)
            print(f"状态码: {response.status_code}")
            if response.status_code == 401:
                print("✓ 登出后token正确失效")
            else:
                print("✗ 登出后token应该失效")
        except Exception as e:
            print(f"请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_auth()
